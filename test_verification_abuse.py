#!/usr/bin/env python3
"""
验证码防滥用功能测试脚本
用于测试验证码发送的频率限制和防滥用机制
"""

import requests
import time
import json
from datetime import datetime

# 测试配置
BASE_URL = "http://localhost:5000"  # 根据实际情况修改
TEST_EMAIL = "<EMAIL>"
SEND_CODE_URL = f"{BASE_URL}/send_verification_code"

def test_send_verification_code(email, description=""):
    """测试发送验证码"""
    print(f"\n{'='*50}")
    print(f"测试: {description}")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"邮箱: {email}")
    
    try:
        response = requests.post(
            SEND_CODE_URL,
            json={"email": email},
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        result = response.json()
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        return result.get('success', False), result.get('message', '')
        
    except Exception as e:
        print(f"请求失败: {e}")
        return False, str(e)

def test_rate_limiting():
    """测试频率限制"""
    print("\n" + "="*80)
    print("开始测试验证码发送频率限制")
    print("="*80)
    
    # 测试1: 正常发送
    success, message = test_send_verification_code(
        TEST_EMAIL, 
        "正常发送验证码"
    )
    
    if not success:
        print(f"❌ 正常发送失败: {message}")
        return
    
    print("✅ 正常发送成功")
    
    # 测试2: 立即重复发送（应该被限制）
    success, message = test_send_verification_code(
        TEST_EMAIL, 
        "立即重复发送（应该被限制）"
    )
    
    if success:
        print("❌ 频率限制失效！立即重复发送竟然成功了")
    else:
        print(f"✅ 频率限制生效: {message}")
    
    # 测试3: 等待一段时间后再次发送
    print("\n等待65秒后再次测试...")
    time.sleep(65)
    
    success, message = test_send_verification_code(
        TEST_EMAIL, 
        "等待65秒后发送"
    )
    
    if success:
        print("✅ 等待后发送成功")
    else:
        print(f"❌ 等待后发送失败: {message}")

def test_multiple_emails():
    """测试多个邮箱发送"""
    print("\n" + "="*80)
    print("测试多个邮箱发送")
    print("="*80)
    
    test_emails = [
        "<EMAIL>",
        "<EMAIL>", 
        "<EMAIL>"
    ]
    
    for i, email in enumerate(test_emails, 1):
        success, message = test_send_verification_code(
            email,
            f"测试邮箱 {i}"
        )
        
        if success:
            print(f"✅ 邮箱 {email} 发送成功")
        else:
            print(f"❌ 邮箱 {email} 发送失败: {message}")
        
        # 短暂延迟避免过快请求
        time.sleep(2)

def test_ip_rate_limiting():
    """测试IP频率限制"""
    print("\n" + "="*80)
    print("测试IP频率限制（快速连续发送）")
    print("="*80)
    
    test_emails = [f"test{i}@qq.com" for i in range(1, 12)]  # 11个邮箱
    
    success_count = 0
    failed_count = 0
    
    for i, email in enumerate(test_emails, 1):
        success, message = test_send_verification_code(
            email,
            f"IP限制测试 {i}/11"
        )
        
        if success:
            success_count += 1
            print(f"✅ 第{i}次发送成功")
        else:
            failed_count += 1
            print(f"❌ 第{i}次发送失败: {message}")
        
        # 很短的延迟
        time.sleep(0.5)
    
    print(f"\n📊 测试结果:")
    print(f"成功: {success_count}")
    print(f"失败: {failed_count}")
    
    if failed_count > 0:
        print("✅ IP频率限制生效")
    else:
        print("❌ IP频率限制可能失效")

def main():
    """主测试函数"""
    print("验证码防滥用功能测试")
    print("请确保服务器正在运行")
    
    # 检查服务器连接
    try:
        response = requests.get(BASE_URL, timeout=5)
        print(f"✅ 服务器连接正常 (状态码: {response.status_code})")
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        return
    
    # 运行测试
    try:
        # 测试1: 基本频率限制
        test_rate_limiting()
        
        # 等待一段时间
        print("\n等待70秒后进行下一组测试...")
        time.sleep(70)
        
        # 测试2: 多邮箱发送
        test_multiple_emails()
        
        # 等待一段时间
        print("\n等待10秒后进行IP限制测试...")
        time.sleep(10)
        
        # 测试3: IP频率限制
        test_ip_rate_limiting()
        
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n\n测试过程中出现错误: {e}")
    
    print("\n" + "="*80)
    print("测试完成")
    print("="*80)

if __name__ == "__main__":
    main()
