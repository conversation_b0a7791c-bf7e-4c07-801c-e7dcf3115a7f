import hashlib
import secrets
import json
import os
import random
import requests
from datetime import datetime, timedelta
from functools import wraps
from flask import session, request, jsonify, redirect, url_for
from collections import defaultdict

# 导入北京时间工具
from beijing_time import beijing_now_iso

class UserManager:
    def __init__(self, data_file='users.json'):
        self.data_file = data_file
        self.users = self.load_users()
        self.verification_codes = {}  # 存储验证码信息
        self.email_send_history = defaultdict(list)  # 邮箱发送历史
        self.ip_send_history = defaultdict(list)  # IP发送历史
    
    def load_users(self):
        """加载用户数据"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, IOError):
                return {}
        return {}
    
    def sanitize_user_data(self, users_data):
        """清理用户数据中的文本字段，防止特殊字符破坏JSON格式"""
        sanitized_data = {}

        for username, user_data in users_data.items():
            sanitized_user = user_data.copy()

            # 清理可能包含用户输入的文本字段
            text_fields = ['registration_reason', 'email', 'linux_do_name', 'linux_do_username']

            for field in text_fields:
                if field in sanitized_user and isinstance(sanitized_user[field], str):
                    sanitized_user[field] = self.sanitize_text_input(sanitized_user[field])

            sanitized_data[username] = sanitized_user

        return sanitized_data

    def save_users(self):
        """保存用户数据，增强错误处理和编码安全性"""
        import tempfile
        import shutil
        from datetime import datetime

        try:
            # 创建带时间戳的备份
            if os.path.exists(self.data_file):
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_file = f"{self.data_file}.backup_{timestamp}"
                try:
                    with open(self.data_file, 'r', encoding='utf-8') as src:
                        with open(backup_file, 'w', encoding='utf-8') as dst:
                            dst.write(src.read())
                except UnicodeDecodeError:
                    # 如果原文件有编码问题，使用二进制复制
                    shutil.copy2(self.data_file, backup_file)

                # 保持最新的简单备份
                simple_backup = f"{self.data_file}.backup"
                try:
                    shutil.copy2(backup_file, simple_backup)
                except:
                    pass

                # 清理旧的带时间戳的备份文件，只保留最近的5个
                self._cleanup_old_backups()

            # 清理用户数据中的文本字段
            sanitized_users = self.sanitize_user_data(self.users)

            # 使用临时文件确保原子性写入
            temp_file = None
            try:
                # 在同一目录下创建临时文件，避免跨驱动器问题
                dir_name = os.path.dirname(os.path.abspath(self.data_file))
                with tempfile.NamedTemporaryFile(mode='w', encoding='utf-8',
                                               delete=False, suffix='.tmp',
                                               dir=dir_name) as f:
                    temp_file = f.name
                    # 使用清理后的数据进行保存
                    json.dump(sanitized_users, f, ensure_ascii=False, indent=2)
                    f.flush()
                    os.fsync(f.fileno())  # 确保数据写入磁盘

                # 原子性替换原文件
                try:
                    if os.name == 'nt':  # Windows
                        # Windows 上使用 replace 可能有问题，先尝试 replace，失败则用 copy + remove
                        try:
                            os.replace(temp_file, self.data_file)
                        except OSError:
                            # 如果 replace 失败，使用 copy + remove
                            shutil.copy2(temp_file, self.data_file)
                            os.unlink(temp_file)
                            temp_file = None  # 标记已删除
                    else:  # Unix/Linux
                        os.replace(temp_file, self.data_file)
                except Exception as e:
                    # 如果原子操作失败，回退到直接写入
                    print(f"原子写入失败，回退到直接写入: {e}")
                    with open(self.data_file, 'w', encoding='utf-8') as f:
                        json.dump(sanitized_users, f, ensure_ascii=False, indent=2)

                return True

            except Exception as e:
                # 清理临时文件
                if temp_file and os.path.exists(temp_file):
                    try:
                        os.unlink(temp_file)
                    except:
                        pass
                raise e

        except Exception as e:
            # 记录错误但不抛出异常，避免影响应用程序运行
            print(f"保存用户数据时出错: {e}")
            return False

    def _cleanup_old_backups(self, max_backups=5):
        """清理旧的带时间戳的备份文件，只保留最近的几个

        Args:
            max_backups: 最多保留的备份文件数量，默认5个
        """
        try:
            import glob
            from datetime import datetime

            # 获取所有带时间戳的备份文件
            backup_pattern = f"{self.data_file}.backup_*"
            backup_files = glob.glob(backup_pattern)

            if len(backup_files) <= max_backups:
                return  # 备份文件数量未超过限制，无需清理

            # 按文件修改时间排序，最新的在前
            backup_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)

            # 删除多余的旧备份文件
            files_to_delete = backup_files[max_backups:]
            deleted_count = 0

            for backup_file in files_to_delete:
                try:
                    os.remove(backup_file)
                    deleted_count += 1
                    print(f"已删除旧备份文件: {os.path.basename(backup_file)}")
                except Exception as e:
                    print(f"删除备份文件失败 {backup_file}: {e}")

            if deleted_count > 0:
                print(f"清理完成，删除了 {deleted_count} 个旧备份文件，保留最近的 {max_backups} 个")

        except Exception as e:
            print(f"清理旧备份文件时出错: {e}")
    
    def hash_password(self, password):
        """密码加密"""
        salt = secrets.token_hex(16)
        password_hash = hashlib.pbkdf2_hmac('sha256', 
                                          password.encode('utf-8'), 
                                          salt.encode('utf-8'), 
                                          100000)
        return salt + password_hash.hex()
    
    def verify_password(self, password, hashed_password):
        """验证密码"""
        salt = hashed_password[:32]
        stored_hash = hashed_password[32:]
        password_hash = hashlib.pbkdf2_hmac('sha256',
                                          password.encode('utf-8'),
                                          salt.encode('utf-8'),
                                          100000)
        return password_hash.hex() == stored_hash
    
    def sanitize_text_input(self, text):
        """清理文本输入，防止特殊字符破坏JSON格式"""
        if not text:
            return ''

        # 移除或替换可能破坏JSON的字符
        # 保留基本的标点符号，但移除控制字符和一些特殊字符
        import re

        # 移除控制字符（除了换行符和制表符）
        text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)

        # 限制长度，防止过长的输入
        if len(text) > 500:
            text = text[:500]

        # 移除连续的空白字符
        text = re.sub(r'\s+', ' ', text)

        return text.strip()

    def register_user(self, username, password, email=None, reason=None, require_approval=False, email_verified=False):
        """用户注册"""
        if username in self.users:
            return False, "用户名已存在"

        if len(username) < 3:
            return False, "用户名至少需要3个字符"

        if len(password) < 6:
            return False, "密码至少需要6个字符"

        # 检查邮箱是否已被使用
        if email:
            for existing_username, user_data in self.users.items():
                if user_data.get('email', '').lower() == email.lower():
                    return False, "该邮箱已被注册"

        # 清理和验证申请理由
        if reason:
            # 先检查原始长度，防止过长输入
            if len(reason) > 500:
                return False, "申请理由不能超过500个字符"

            reason = self.sanitize_text_input(reason)
            if len(reason) < 5:
                return False, "申请理由至少需要5个字符"
        else:
            return False, "申请理由不能为空"

        # 根据系统设置决定用户状态
        if require_approval:
            user_status = 'pending'  # 待审核
            success_message = "注册成功，请等待管理员审核"
            initial_points = 0  # 待审核用户暂不赠送积分
        else:
            user_status = 'approved'  # 直接通过
            success_message = "注册成功，赠送10点积分"
            initial_points = 10  # 新用户赠送10点积分

        # 创建新用户
        user_data = {
            'username': username,
            'password': self.hash_password(password),
            'email': email or '',
            'email_verified': email_verified,  # 邮箱验证状态
            'registration_reason': reason or '',  # 申请理由（已清理）
            'points': initial_points,
            'created_at': beijing_now_iso(),
            'last_login': None,
            'is_admin': False,
            'is_authorized': False,  # 是否为授权用户，只有授权用户才能访问画廊
            'total_generated': 0,  # 总生成次数
            'status': user_status,  # 用户状态：pending, approved（rejected用户会被直接删除）
            'approved_at': None if require_approval else beijing_now_iso(),  # 审核通过时间
            'approved_by': None  # 审核人
        }

        self.users[username] = user_data

        if self.save_users():
            return True, success_message
        else:
            return False, "注册失败，请重试"

    def create_linux_do_user(self, username, linux_do_id, linux_do_username, name=None, email=None, avatar_template=None):
        """创建Linux Do OAuth2用户"""
        if username in self.users:
            return False

        # 创建新用户数据
        user_data = {
            'username': username,
            'password': None,  # OAuth2用户不需要密码
            'email': email or '',
            'points': 10,  # 新用户赠送10点积分
            'created_at': beijing_now_iso(),
            'last_login': beijing_now_iso(),
            'is_admin': False,
            'is_authorized': False,  # 是否为授权用户，只有授权用户才能访问画廊
            'total_generated': 0,
            'status': 'approved',  # OAuth2用户直接通过
            'approved_at': beijing_now_iso(),
            'approved_by': 'system_oauth2',
            # Linux Do 特有字段
            'auth_type': 'linux_do',
            'linux_do_id': linux_do_id,
            'linux_do_username': linux_do_username,
            'linux_do_name': name or '',
            'linux_do_avatar_template': avatar_template or ''
        }

        self.users[username] = user_data
        return self.save_users()
    
    def login_user(self, username, password):
        """用户登录"""
        if username not in self.users:
            return False, "用户名不存在"

        user = self.users[username]
        if not self.verify_password(password, user['password']):
            return False, "密码错误"

        # 检查用户审核状态
        user_status = user.get('status', 'approved')  # 兼容旧数据，默认为已通过
        if user_status == 'pending':
            return False, "您的账户正在等待管理员审核，请耐心等待"
        elif user_status == 'rejected':
            # 注意：正常情况下被拒绝的用户账户已被删除，这里是为了兼容可能的遗留数据
            return False, "您的账户审核未通过，请联系管理员"

        # 更新最后登录时间
        user['last_login'] = beijing_now_iso()
        self.save_users()

        return True, "登录成功"
    
    def get_user(self, username):
        """获取用户信息"""
        return self.users.get(username)
    
    def update_user_points(self, username, points_change):
        """更新用户积分"""
        if username not in self.users:
            return False, "用户不存在"
        
        user = self.users[username]
        new_points = user['points'] + points_change
        
        if new_points < 0:
            return False, "积分不足"
        
        user['points'] = new_points
        self.save_users()
        return True, f"积分更新成功，当前积分：{new_points}"
    
    def increment_generation_count(self, username):
        """增加用户的生成次数计数

        Args:
            username: 用户名

        Returns:
            bool: 是否成功
        """
        if username not in self.users:
            return False

        user = self.users[username]
        user['total_generated'] += 1
        self.save_users()
        return True



    def get_user_stats(self, username):
        """获取用户统计信息"""
        if username not in self.users:
            return None

        user = self.users[username]

        return {
            'username': username,
            'points': user['points'],
            'total_generated': user['total_generated'],
            'created_at': user['created_at'],
            'last_login': user['last_login'],
            'status': user.get('status', 'approved'),  # 兼容旧数据
            'is_admin': user.get('is_admin', False)
        }



    def approve_user(self, username, admin_username):
        """管理员审核通过用户"""
        if username not in self.users:
            return False, "用户不存在"

        user = self.users[username]
        if user.get('status') != 'pending':
            return False, "用户不在待审核状态"

        # 更新用户状态
        user['status'] = 'approved'
        user['approved_at'] = beijing_now_iso()
        user['approved_by'] = admin_username
        user['points'] = 10  # 审核通过后赠送积分

        if self.save_users():
            return True, "用户审核通过，已赠送10点积分"
        else:
            return False, "审核操作失败"

    def reject_user(self, username, admin_username, reason=""):
        """管理员拒绝用户 - 彻底删除账户避免污染数据库"""
        if username not in self.users:
            return False, "用户不存在"

        user = self.users[username]
        if user.get('status') != 'pending':
            return False, "用户不在待审核状态"

        # 记录拒绝操作到日志（用于审计）
        rejected_user_info = {
            'username': username,
            'rejected_at': beijing_now_iso(),
            'rejected_by': admin_username,
            'reject_reason': reason,
            'user_data': user.copy()  # 备份用户数据用于审计
        }

        # 彻底删除用户账户
        del self.users[username]

        if self.save_users():
            # 记录拒绝删除操作到日志文件
            self._log_user_rejection(rejected_user_info)
            return True, f"用户 {username} 审核被拒绝，账户已彻底删除"
        else:
            # 如果保存失败，恢复用户数据
            self.users[username] = user
            return False, "审核操作失败"

    def get_pending_users(self):
        """获取待审核用户列表"""
        pending_users = []
        for username, user_data in self.users.items():
            if user_data.get('status') == 'pending':
                pending_users.append({
                    'username': username,
                    'email': user_data.get('email', ''),
                    'registration_reason': user_data.get('registration_reason', ''),
                    'created_at': user_data.get('created_at', ''),
                    'status': user_data.get('status', 'pending')
                })

        # 按注册时间排序，最新的在前
        pending_users.sort(key=lambda x: x['created_at'], reverse=True)
        return pending_users

    def reset_user_password(self, username, new_password, admin_username):
        """管理员重置用户密码"""
        if username not in self.users:
            return False, "用户不存在"

        if len(new_password) < 6:
            return False, "密码至少需要6个字符"

        # 更新用户密码
        self.users[username]['password'] = self.hash_password(new_password)
        self.users[username]['password_reset_at'] = beijing_now_iso()
        self.users[username]['password_reset_by'] = admin_username

        if self.save_users():
            return True, f"用户 {username} 的密码已重置"
        else:
            return False, "密码重置失败"

    def delete_user(self, username, admin_username):
        """管理员删除用户"""
        if username not in self.users:
            return False, "用户不存在"

        # 记录删除操作（可选：保存到日志文件）
        deleted_user_info = {
            'username': username,
            'deleted_at': beijing_now_iso(),
            'deleted_by': admin_username,
            'user_data': self.users[username].copy()  # 备份用户数据
        }

        # 删除用户
        del self.users[username]

        if self.save_users():
            # 可以选择将删除记录保存到单独的日志文件
            self._log_user_deletion(deleted_user_info)
            return True, f"用户 {username} 已删除"
        else:
            return False, "删除用户失败"

    def set_user_authorized(self, username, admin_username, authorized=True):
        """管理员设置用户授权状态"""
        if username not in self.users:
            return False, "用户不存在"

        user = self.users[username]
        old_status = user.get('is_authorized', False)

        # 更新授权状态
        user['is_authorized'] = authorized
        user['authorized_at'] = beijing_now_iso() if authorized else None
        user['authorized_by'] = admin_username if authorized else None

        if self.save_users():
            action = "设置为授权用户" if authorized else "取消授权用户"
            return True, f"用户 {username} 已{action}"
        else:
            # 回滚状态
            user['is_authorized'] = old_status
            return False, "授权状态更新失败"

    def get_authorized_users(self):
        """获取所有授权用户列表"""
        authorized_users = []
        for username, user in self.users.items():
            if user.get('is_authorized', False):
                authorized_users.append({
                    'username': username,
                    'email': user.get('email', ''),
                    'points': user.get('points', 0),
                    'created_at': user.get('created_at', ''),
                    'authorized_at': user.get('authorized_at', ''),
                    'authorized_by': user.get('authorized_by', ''),
                    'is_admin': user.get('is_admin', False)
                })
        return authorized_users

    def is_user_authorized(self, username):
        """检查用户是否为授权用户"""
        if username not in self.users:
            return False
        user = self.users[username]
        return user.get('is_authorized', False) or user.get('is_admin', False)  # 管理员默认有授权

    def _log_user_deletion(self, deletion_info):
        """记录用户删除日志"""
        try:
            log_file = 'deleted_users.json'
            deleted_users = []

            # 读取现有的删除记录
            if os.path.exists(log_file):
                try:
                    with open(log_file, 'r', encoding='utf-8') as f:
                        deleted_users = json.load(f)
                except (json.JSONDecodeError, IOError):
                    deleted_users = []

            # 添加新的删除记录
            deleted_users.append(deletion_info)

            # 保存删除记录
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(deleted_users, f, ensure_ascii=False, indent=2)
        except Exception as e:
            # 记录日志失败不影响删除操作
            print(f"记录删除日志失败: {e}")

    def _log_user_rejection(self, rejection_info):
        """记录用户拒绝日志"""
        try:
            log_file = 'rejected_users.json'
            rejected_users = []

            # 读取现有的拒绝记录
            if os.path.exists(log_file):
                try:
                    with open(log_file, 'r', encoding='utf-8') as f:
                        rejected_users = json.load(f)
                except (json.JSONDecodeError, IOError):
                    rejected_users = []

            # 添加新的拒绝记录
            rejected_users.append(rejection_info)

            # 保存拒绝记录
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(rejected_users, f, ensure_ascii=False, indent=2)
        except Exception as e:
            # 记录日志失败不影响拒绝操作
            print(f"记录拒绝日志失败: {e}")

    def generate_api_key(self, username):
        """为用户生成API密钥"""
        if username not in self.users:
            return False, "用户不存在"

        user = self.users[username]

        # 生成新的API密钥
        api_key = f"sk-{secrets.token_urlsafe(32)}"

        # 保存API密钥信息
        user['api_key'] = {
            'key': api_key,
            'created_at': beijing_now_iso(),
            'last_used': None,
            'usage_count': 0
        }

        if self.save_users():
            return True, api_key
        else:
            return False, "生成API密钥失败"

    def get_api_key(self, username):
        """获取用户的API密钥信息"""
        if username not in self.users:
            return None

        user = self.users[username]
        return user.get('api_key')

    def delete_api_key(self, username):
        """删除用户的API密钥"""
        if username not in self.users:
            return False, "用户不存在"

        user = self.users[username]
        if 'api_key' not in user:
            return False, "用户没有API密钥"

        # 删除API密钥
        del user['api_key']

        if self.save_users():
            return True, "API密钥已删除"
        else:
            return False, "删除API密钥失败"

    def verify_api_key(self, api_key):
        """验证API密钥并返回用户信息"""
        if not api_key or not api_key.startswith('sk-'):
            return None

        # 遍历所有用户查找匹配的API密钥
        for username, user in self.users.items():
            user_api_key = user.get('api_key')
            if user_api_key and user_api_key.get('key') == api_key:
                # 更新使用记录
                user_api_key['last_used'] = beijing_now_iso()
                user_api_key['usage_count'] = user_api_key.get('usage_count', 0) + 1
                self.save_users()

                return {
                    'username': username,
                    'user_data': user
                }

        return None

    def cleanup_send_history(self):
        """清理发送历史记录"""
        current_time = datetime.now()

        # 清理邮箱发送历史（保留1小时内的记录）
        for email in list(self.email_send_history.keys()):
            self.email_send_history[email] = [
                timestamp for timestamp in self.email_send_history[email]
                if current_time - timestamp < timedelta(hours=1)
            ]
            if not self.email_send_history[email]:
                del self.email_send_history[email]

        # 清理IP发送历史（保留1小时内的记录）
        for ip in list(self.ip_send_history.keys()):
            self.ip_send_history[ip] = [
                timestamp for timestamp in self.ip_send_history[ip]
                if current_time - timestamp < timedelta(hours=1)
            ]
            if not self.ip_send_history[ip]:
                del self.ip_send_history[ip]

    def check_send_rate_limit(self, email, client_ip):
        """检查发送频率限制"""
        current_time = datetime.now()

        # 清理历史记录
        self.cleanup_send_history()

        # 检查同一邮箱的发送频率
        email_history = self.email_send_history.get(email, [])

        # 1分钟内不能重复发送
        recent_sends = [t for t in email_history if current_time - t < timedelta(minutes=1)]
        if recent_sends:
            remaining_seconds = 60 - int((current_time - recent_sends[-1]).total_seconds())
            return False, f"发送过于频繁，请等待{remaining_seconds}秒后再试"

        # 1小时内最多发送5次
        if len(email_history) >= 5:
            return False, "该邮箱1小时内发送次数已达上限（5次），请稍后再试"

        # 检查同一IP的发送频率
        ip_history = self.ip_send_history.get(client_ip, [])

        # 1分钟内最多发送2次
        recent_ip_sends = [t for t in ip_history if current_time - t < timedelta(minutes=1)]
        if len(recent_ip_sends) >= 2:
            return False, "发送过于频繁，请稍后再试"

        # 1小时内最多发送10次
        if len(ip_history) >= 10:
            return False, "您的IP地址1小时内发送次数已达上限，请稍后再试"

        return True, "检查通过"

    def record_send_attempt(self, email, client_ip):
        """记录发送尝试"""
        current_time = datetime.now()
        self.email_send_history[email].append(current_time)
        self.ip_send_history[client_ip].append(current_time)

    def generate_verification_code(self, email, client_ip):
        """生成邮箱验证码"""
        # 检查是否已有未过期的验证码
        if email in self.verification_codes:
            code_info = self.verification_codes[email]
            if datetime.now() < code_info['expires_at']:
                remaining_seconds = int((code_info['expires_at'] - datetime.now()).total_seconds())
                return None, f"验证码仍有效，请等待{remaining_seconds}秒后再重新发送"

        # 检查发送频率限制
        rate_check, rate_message = self.check_send_rate_limit(email, client_ip)
        if not rate_check:
            return None, rate_message

        # 生成6位数字验证码
        code = str(random.randint(100000, 999999))

        # 存储验证码信息，有效期5分钟
        self.verification_codes[email] = {
            'code': code,
            'created_at': datetime.now(),
            'expires_at': datetime.now() + timedelta(minutes=5),
            'attempts': 0,  # 验证尝试次数
            'client_ip': client_ip  # 记录请求IP
        }

        # 记录发送尝试
        self.record_send_attempt(email, client_ip)

        return code, "验证码生成成功"

    def send_verification_email(self, email, code):
        """发送验证码邮件"""
        email_api_url = "https://prod-93.southeastasia.logic.azure.com:443/workflows/ffb2c79e198f4376998f5398cfd800cd/triggers/manual/paths/invoke?api-version=2016-06-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=q0Nv7AId-Ap-ZW57SC4CMyPvG7II4ysG6IrZt3ZC-oo"

        email_data = {
            "mail": email,
            "title": "【喵喵AI】邮箱验证码",
            "text": f"""
您好！

您正在注册喵喵AI账户，验证码为：{code}

验证码有效期为5分钟，请及时使用。

如果这不是您的操作，请忽略此邮件。

---
喵喵AI团队
            """.strip()
        }

        try:
            response = requests.post(email_api_url, json=email_data, timeout=30)
            # 200和202都视为成功，202表示请求已接受正在处理
            if response.status_code in [200, 202]:
                return True, "验证码已发送到您的邮箱"
            else:
                return False, f"邮件发送失败，状态码：{response.status_code}"
        except requests.RequestException as e:
            return False, f"邮件发送失败：{str(e)}"

    def verify_email_code(self, email, code):
        """验证邮箱验证码"""
        if email not in self.verification_codes:
            return False, "验证码不存在或已过期"

        code_info = self.verification_codes[email]

        # 检查验证码是否过期
        if datetime.now() > code_info['expires_at']:
            del self.verification_codes[email]
            return False, "验证码已过期，请重新获取"

        # 检查尝试次数
        if code_info['attempts'] >= 3:
            del self.verification_codes[email]
            return False, "验证码尝试次数过多，请重新获取"

        # 验证码码
        if code_info['code'] != code:
            code_info['attempts'] += 1
            return False, f"验证码错误，还可尝试{3 - code_info['attempts']}次"

        # 验证成功，删除验证码
        del self.verification_codes[email]
        return True, "邮箱验证成功"

    def cleanup_expired_codes(self):
        """清理过期的验证码"""
        current_time = datetime.now()
        expired_emails = []

        for email, code_info in self.verification_codes.items():
            if current_time > code_info['expires_at']:
                expired_emails.append(email)

        for email in expired_emails:
            del self.verification_codes[email]

    def get_verification_stats(self):
        """获取验证码发送统计信息（管理员功能）"""
        current_time = datetime.now()

        # 清理历史记录
        self.cleanup_send_history()

        stats = {
            'active_codes': len(self.verification_codes),
            'email_send_history': {},
            'ip_send_history': {},
            'recent_activity': []
        }

        # 统计邮箱发送历史
        for email, timestamps in self.email_send_history.items():
            stats['email_send_history'][email] = {
                'total_sends': len(timestamps),
                'last_send': timestamps[-1].isoformat() if timestamps else None,
                'sends_last_hour': len([t for t in timestamps if current_time - t < timedelta(hours=1)])
            }

        # 统计IP发送历史
        for ip, timestamps in self.ip_send_history.items():
            stats['ip_send_history'][ip] = {
                'total_sends': len(timestamps),
                'last_send': timestamps[-1].isoformat() if timestamps else None,
                'sends_last_hour': len([t for t in timestamps if current_time - t < timedelta(hours=1)])
            }

        # 最近活动（最近1小时的发送记录）
        for email, code_info in self.verification_codes.items():
            if current_time - code_info['created_at'] < timedelta(hours=1):
                stats['recent_activity'].append({
                    'email': email,
                    'created_at': code_info['created_at'].isoformat(),
                    'expires_at': code_info['expires_at'].isoformat(),
                    'client_ip': code_info.get('client_ip', 'unknown'),
                    'attempts': code_info['attempts']
                })

        # 按时间排序最近活动
        stats['recent_activity'].sort(key=lambda x: x['created_at'], reverse=True)

        return stats

# 装饰器：要求用户登录
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'username' not in session:
            if request.is_json:
                return jsonify({'success': False, 'message': '请先登录'}), 401
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

# 装饰器：要求管理员权限
def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'username' not in session:
            if request.is_json:
                return jsonify({'success': False, 'message': '请先登录'}), 401
            return redirect(url_for('login'))
        
        # 这里需要在app.py中初始化user_manager后才能使用
        # 暂时简化处理
        return f(*args, **kwargs)
    return decorated_function

# 装饰器：检查积分是否足够
def points_required(points_needed=1):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if 'username' not in session:
                if request.is_json:
                    return jsonify({'success': False, 'message': '请先登录'}), 401
                return redirect(url_for('login'))
            
            # 这里需要在具体使用时检查积分
            return f(*args, **kwargs)
        return decorated_function
    return decorator
